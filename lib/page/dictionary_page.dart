import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/service/translate/index.dart';
import 'package:dasso_reader/widgets/dictionary/accessible_dictionary_tab.dart';
import 'package:dasso_reader/widgets/dictionary/main_dictionary_tab.dart';

/// A dedicated page for dictionary lookups
class DictionaryPage extends StatefulWidget {
  /// Initial search query, if any
  final String? initialQuery;

  const DictionaryPage({
    super.key,
    this.initialQuery,
  });

  @override
  State<DictionaryPage> createState() => _DictionaryPageState();
}

class _DictionaryPageState extends State<DictionaryPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _isSearching = false;
  List<DictionaryEntry> _searchResults = [];
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      _searchController.text = widget.initialQuery!;
      _performSearch();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  /// Perform a search with the current query
  Future<void> _performSearch() async {
    final query = _searchController.text.trim();

    if (query.isEmpty) {
      if (!mounted) return;
      setState(() {
        _isSearching = false;
        _searchResults = [];
        _hasError = false;
      });
      return;
    }

    if (!mounted) return;
    setState(() {
      _isSearching = true;
      _hasError = false;
    });

    try {
      // Add a timeout to prevent endless searches
      final result = await Future.any([
        _performActualSearch(query),
        // Add a 5-second timeout that returns an empty result
        Future.delayed(const Duration(seconds: 5), () => <DictionaryEntry>[]),
      ]);

      // Check if widget is still mounted before updating state
      if (!mounted) return;

      // Update state based on results
      setState(() {
        _searchResults = result;
        _isSearching = false;

        if (result.isEmpty) {
          _hasError = true;
          _errorMessage = 'No dictionary entries found for "$query"';

          // Even if we don't have dictionary entries, enable character study for Chinese text
          if (_isChinese(query)) {
            // We still want to allow character study for any Chinese text
            _errorMessage =
                'No dictionary entries found for "$query".\nYou can still study individual characters.';
          }
        }
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isSearching = false;
        _hasError = true;
        _errorMessage = 'Error searching dictionary: $e';
      });
    }
  }

  /// Perform the actual search operation separated from timeout logic
  Future<List<DictionaryEntry>> _performActualSearch(String query) async {
    // Determine if the query is Chinese or English
    final isChinese = _isChinese(query);

    if (isChinese) {
      // For Chinese queries, look up the word directly
      final entry = await DictionaryService().lookupChinese(query);

      if (entry != null) {
        return [entry];
      } else {
        // If no exact match, try to find words containing the query
        final relatedWords =
            await DictionaryService().findWordsWithCharacter(query);

        return relatedWords;
      }
    } else {
      // For English queries, search in definitions
      return await DictionaryService().lookupEnglish(query);
    }
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// Open the batch character study page
  void _openBatchStudyPage(BuildContext context, String text) {
    Navigator.push(
      context,
      MaterialPageRoute<void>(
        builder: (context) => BatchCharacterStudyPage(text: text),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      // Removed AppBar to save space
      body: CustomScrollView(
        slivers: [
          // Enhanced search area - moved to the top by removing the title
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: Column(
                children: [
                  // Modern Material 3 style search field with info button
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest
                          .withAlpha((0.5 * 255).round()),
                      borderRadius:
                          BorderRadius.circular(DesignSystem.radiusCircle),
                      border: Border.all(
                        color:
                            colorScheme.outline.withAlpha((0.3 * 255).round()),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Icon(
                            Icons.search_rounded,
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Expanded(
                          child: SemanticHelpers.textField(
                            child: TextField(
                              controller: _searchController,
                              focusNode: _searchFocusNode,
                              decoration: InputDecoration(
                                hintText:
                                    '${L10n.of(context).common_search} ${L10n.of(context).common_dictionary}',
                                border: InputBorder.none,
                                contentPadding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                hintStyle: TextStyle(
                                  color: colorScheme.onSurfaceVariant
                                      .withAlpha((0.7 * 255).round()),
                                ),
                              ),
                              style: TextStyle(color: colorScheme.onSurface),
                              onSubmitted: (_) => _performSearch(),
                              textInputAction: TextInputAction.search,
                            ),
                            label:
                                '${L10n.of(context).common_search} ${L10n.of(context).common_dictionary}',
                            hint:
                                'Enter Chinese characters, pinyin, or English words to search',
                            value: _searchController.text,
                          ),
                        ),
                        if (_searchController.text.isNotEmpty)
                          SemanticHelpers.button(
                            context: context,
                            child: IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: colorScheme.onSurfaceVariant,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchResults = [];
                                  _hasError = false;
                                });
                              },
                            ),
                            label: 'Clear search',
                            hint: 'Clear the search field and results',
                            onTap: () {
                              _searchController.clear();
                              setState(() {
                                _searchResults = [];
                                _hasError = false;
                              });
                            },
                          ),
                        // Batch mode toggle for character study
                        if (_searchResults.isNotEmpty &&
                            _isChinese(_searchController.text))
                          SemanticHelpers.button(
                            context: context,
                            child: IconButton(
                              icon: const Icon(
                                Icons.brush,
                                size: 20,
                              ),
                              onPressed: () => _openBatchStudyPage(
                                context,
                                _searchResults.length == 1
                                    ? _searchResults[0].simplified
                                    : _searchController.text,
                              ),
                              tooltip: 'Stroke Order Practice',
                            ),
                            label: 'Stroke Order Practice',
                            hint:
                                'Open stroke order practice for character learning',
                            onTap: () => _openBatchStudyPage(
                              context,
                              _searchResults.length == 1
                                  ? _searchResults[0].simplified
                                  : _searchController.text,
                            ),
                          ),
                        // Info button moved to search bar right side
                        SemanticHelpers.button(
                          context: context,
                          child: IconButton(
                            icon: Icon(
                              Icons.info_outline,
                              color: colorScheme.onSurfaceVariant,
                              size: 20,
                            ),
                            onPressed: () => _showDictionaryInfo(context),
                            tooltip: 'Dictionary Information',
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                            visualDensity: VisualDensity.compact,
                          ),
                          label: 'Dictionary Information',
                          hint:
                              'View information about the dictionary and its features',
                          onTap: () => _showDictionaryInfo(context),
                        ),
                        const SizedBox(width: 8),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Spacer
          const SliverToBoxAdapter(
            child: SizedBox(height: 16),
          ),

          // Results area
          _buildSearchResultsSliver(),
        ],
      ),
    );
  }

  /// Build the search results as a sliver
  Widget _buildSearchResultsSliver() {
    if (_isSearching) {
      return const SliverFillRemaining(
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_hasError) {
      return SliverFillRemaining(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(DesignSystem.spaceM),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Use a translation icon instead of error icon for Chinese text
                Icon(
                  _isChinese(_searchController.text)
                      ? Icons.translate
                      : Icons.error_outline,
                  color: _isChinese(_searchController.text)
                      ? Theme.of(context).colorScheme.primary
                      : Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),

                // Show original query text
                if (_isChinese(_searchController.text))
                  Text(
                    _searchController.text,
                    style: Theme.of(context).textTheme.titleLarge,
                    textAlign: TextAlign.center,
                  ),

                // Show error message
                Text(
                  _errorMessage,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),

                // Show translation for Chinese text
                if (_isChinese(_searchController.text)) _buildTranslationCard(),

                // Add a character study button for Chinese text
                if (_isChinese(_searchController.text))
                  Padding(
                    padding: const EdgeInsets.only(top: 24.0),
                    child: ElevatedButton.icon(
                      onPressed: () => _openBatchStudyPage(
                        context,
                        _searchController.text,
                      ),
                      icon: const Icon(Icons.brush),
                      label: const Text('Practice Strokes'),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.search,
                size: 64,
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withAlpha((0.4 * 255).round()),
              ),
              const SizedBox(height: 16),
              Text(
                '${L10n.of(context).common_search} ${L10n.of(context).common_dictionary}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withAlpha((0.7 * 255).round()),
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // If there's only one result, show the full dictionary tab
    if (_searchResults.length == 1) {
      return SliverFillRemaining(
        child: MainDictionaryTab(
          text: _searchResults[0].simplified,
          limitDefinitions: false,
        ),
      );
    }

    // Otherwise, show a list of results
    return SliverPadding(
      padding: const EdgeInsets.all(DesignSystem.spaceM),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final entry = _searchResults[index];
            return Column(
              children: [
                ListTile(
                  title: Text(
                    entry.simplified,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        entry.formattedPinyin(),
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      Text(
                        entry.definitions.first,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute<void>(
                        builder: (context) => Scaffold(
                          appBar: AppBar(
                            title: Text(entry.simplified),
                            actions: [
                              // Add character study button in detail view
                              if (_isChinese(entry.simplified))
                                SemanticHelpers.button(
                                  context: context,
                                  label: 'Stroke Order Practice',
                                  hint:
                                      'Open stroke order practice for character learning',
                                  onTap: () => _openBatchStudyPage(
                                    context,
                                    entry.simplified,
                                  ),
                                  child: IconButton(
                                    icon: const Icon(Icons.brush),
                                    onPressed: () => _openBatchStudyPage(
                                      context,
                                      entry.simplified,
                                    ),
                                    tooltip: 'Stroke Order Practice',
                                  ),
                                ),
                            ],
                          ),
                          body: MainDictionaryTab(
                            text: entry.simplified,
                            limitDefinitions: false,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                if (index < _searchResults.length - 1) const Divider(),
              ],
            );
          },
          childCount: _searchResults.length,
        ),
      ),
    );
  }

  /// Build a card that shows the translation
  Widget _buildTranslationCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: FutureBuilder<String>(
        future: _getTranslation(_searchController.text),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Padding(
              padding: EdgeInsets.all(DesignSystem.spaceM),
              child: CircularProgressIndicator(),
            );
          }

          if (snapshot.hasError) {
            return Card(
              elevation: 0,
              color: Theme.of(context)
                  .colorScheme
                  .errorContainer
                  .withAlpha((0.5 * 255).round()),
              child: Padding(
                padding: const EdgeInsets.all(DesignSystem.spaceM),
                child: Text(
                  'Translation error: ${snapshot.error}',
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
              ),
            );
          }

          return Card(
            elevation: 0,
            color: Theme.of(context)
                .colorScheme
                .surfaceContainerHighest
                .withAlpha((0.5 * 255).round()),
            child: Padding(
              padding: const EdgeInsets.all(DesignSystem.spaceM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.translate,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Translation:',
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    snapshot.data ?? 'Translation not available',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.start,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Get translation for the text
  Future<String> _getTranslation(String text) async {
    // Use the app's translation service
    return await translateText(text);
  }

  /// Show information about the dictionary
  void _showDictionaryInfo(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Dictionary Information'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'This dictionary is based on the CC-CEDICT Chinese-English dictionary.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text('• Look up Chinese words and characters'),
              Text('• View definitions and pinyin pronunciation'),
              Text('• See stroke order animations'),
              Text('• Find related words and example sentences'),
              SizedBox(height: 8),
              Text(
                'Data Sources:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text('• CC-CEDICT for dictionary entries'),
              Text('• Makemeahanzi for stroke order data'),
              SizedBox(height: 8),
              Text(
                'Character Study Mode:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text('• Study multiple characters in a sequence'),
              Text(
                '• Navigate between characters with prev/next buttons',
              ),
              Text('• Perfect for learning characters in new words'),
              SizedBox(height: 16),
              Text(
                'This dictionary is provided for educational purposes only.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

/// A dedicated page for batch character study with stroke order animations
class BatchCharacterStudyPage extends StatefulWidget {
  /// The text to study, either a single character or a multi-character word
  final String text;

  const BatchCharacterStudyPage({
    super.key,
    required this.text,
  });

  @override
  State<BatchCharacterStudyPage> createState() =>
      _BatchCharacterStudyPageState();
}

class _BatchCharacterStudyPageState extends State<BatchCharacterStudyPage> {
  int _currentIndex = 0;
  List<String> _characters = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _setupCharacters();
  }

  /// Set up the characters for study
  void _setupCharacters() {
    // Extract Chinese characters from the text
    _characters = widget.text.split('').where(_isChinese).toList();

    if (_characters.isEmpty) {
      _characters = [widget.text]; // Fallback to the original text
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// Navigate to the next character
  void _nextCharacter() {
    if (_characters.isEmpty) return;

    setState(() {
      _currentIndex = (_currentIndex + 1) % _characters.length;
    });
  }

  /// Navigate to the previous character
  void _previousCharacter() {
    if (_characters.isEmpty) return;

    setState(() {
      _currentIndex =
          (_currentIndex - 1 + _characters.length) % _characters.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Stroke Practice: ${widget.text}'),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Character progress indicator
                Padding(
                  padding: const EdgeInsets.all(DesignSystem.spaceM),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Character ${_currentIndex + 1} of ${_characters.length}',
                        style: theme.textTheme.titleMedium,
                      ),
                    ],
                  ),
                ),

                // Character navigation dots
                if (_characters.length > 1)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        _characters.length,
                        (index) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 4.0),
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            height: 10,
                            width: index == _currentIndex ? 20 : 10,
                            decoration: BoxDecoration(
                              color: index == _currentIndex
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.primary
                                      .withAlpha((0.3 * 255).round()),
                              borderRadius:
                                  BorderRadius.circular(DesignSystem.radiusS),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                // Main content area - stroke-only practice mode
                Expanded(
                  child: AccessibleDictionaryTab(
                    text: _characters[_currentIndex],
                    showTabBar: false,
                    limitDefinitions: false,
                    batchCharacterMode: true, // Enable stroke animations
                    compactMode: true,
                    showStrokeAnimations: true,
                    strokeOnlyMode: true, // Show only stroke animations, no definitions
                  ),
                ),

                // Bottom navigation controls
                Padding(
                  padding: const EdgeInsets.all(DesignSystem.spaceM),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed:
                            _characters.length > 1 ? _previousCharacter : null,
                        icon: const Icon(Icons.arrow_back),
                        label: const Text('Previous'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed:
                            _characters.length > 1 ? _nextCharacter : null,
                        icon: const Icon(Icons.arrow_forward),
                        label: const Text('Next'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}
