import 'dart:async';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/service/tts/base_tts.dart';
import 'package:dasso_reader/service/tts/edge_tts.dart';
import 'package:dasso_reader/service/tts/system_tts.dart';
import 'package:dasso_reader/utils/error/common.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';

class TtsFactory {
  static final TtsFactory _instance = TtsFactory._internal();

  factory TtsFactory() {
    return _instance;
  }

  TtsFactory._internal();

  BaseTts? _currentTts;

  // Conflict management properties
  TtsContext? _activeContext;
  TtsEngineType? _activeEngine;
  DateTime? _operationStartTime;
  Completer<void>? _currentOperation;
  bool _isLocked = false;

  // Notifier for context changes
  final ValueNotifier<TtsContext?> _contextNotifier = ValueNotifier(null);

  BaseTts get current {
    _currentTts ??= createTtsWithDifferentiation();
    return _currentTts!;
  }

  BaseTts createTts() {
    final bool isSystemTts = Prefs().isSystemTts;
    return isSystemTts ? SystemTts() : EdgeTts();
  }

  /// Create TTS engine with intelligent differentiation to avoid conflicts
  /// Dictionary always uses SystemTts (FlutterTts), so Continuous Reading should prefer EdgeTts
  BaseTts createTtsWithDifferentiation() {
    final bool userPrefersSystemTts = Prefs().isSystemTts;

    // INTELLIGENT ENGINE DIFFERENTIATION:
    // Dictionary pronunciation always uses FlutterTts (SystemTts)
    // So for continuous reading, we prefer EdgeTts to maintain separation

    if (userPrefersSystemTts) {
      // User prefers System TTS, but Dictionary already uses FlutterTts
      // Force continuous reading to use EdgeTts to avoid conflicts
      AnxLog.info(
          'User prefers System TTS, but using EdgeTts for continuous reading to avoid dictionary conflicts');
      return EdgeTts();
    } else {
      // User prefers Edge TTS, which is perfect for separation
      return EdgeTts();
    }
  }

  // Conflict management getters
  TtsContext? get activeContext => _activeContext;
  TtsEngineType? get activeEngine => _activeEngine;
  ValueNotifier<TtsContext?> get contextNotifier => _contextNotifier;
  bool get hasActiveOperation => _activeContext != null;
  bool isContextActive(TtsContext context) => _activeContext == context;

  /// Get information about current engine differentiation
  String get engineDifferentiationInfo {
    final bool userPrefersSystemTts = Prefs().isSystemTts;
    final String continuousReadingEngine =
        userPrefersSystemTts ? 'EdgeTts' : 'EdgeTts';

    return 'Engine Separation Active:\n'
        '• Dictionary: SystemTts (FlutterTts)\n'
        '• Continuous Reading: $continuousReadingEngine\n'
        '• User Preference: ${userPrefersSystemTts ? 'System TTS' : 'Edge TTS'}\n'
        '• Separation: ${userPrefersSystemTts ? 'Forced (conflict prevention)' : 'Natural'}';
  }

  /// Check if engine differentiation is active (forced separation)
  bool get isEngineDifferentiationActive {
    return Prefs()
        .isSystemTts; // True when we force EdgeTts for continuous reading
  }

  /// Validate that engine separation is working correctly
  Map<String, dynamic> validateEngineSeparation() {
    final bool userPrefersSystemTts = Prefs().isSystemTts;
    final BaseTts continuousReadingEngine = createTtsWithDifferentiation();

    return {
      'userPreference': userPrefersSystemTts ? 'System TTS' : 'Edge TTS',
      'dictionaryEngine': 'SystemTts (FlutterTts)',
      'continuousReadingEngine': continuousReadingEngine.runtimeType.toString(),
      'enginesSeparated':
          continuousReadingEngine.runtimeType.toString() != 'SystemTts',
      'differentiationActive': isEngineDifferentiationActive,
      'conflictPrevented': userPrefersSystemTts ? true : false,
      'engineTypes': {
        'dictionary': 'TtsEngineType.systemTts',
        'continuousReading': continuousReadingEngine.engineType.toString(),
      },
    };
  }

  /// Request exclusive access for a TTS operation
  Future<bool> requestAccess(TtsContext context, TtsEngineType engine) async {
    AnxLog.info('TTS access requested for context: $context, engine: $engine');

    // Validate context and engine combination
    if (!_isValidCombination(context, engine)) {
      return false;
    }

    // Cleanup any timed out operations
    await cleanupTimedOut();

    // If already locked by same context, allow
    if (_activeContext == context && _activeEngine == engine) {
      AnxLog.info('Access granted - same context and engine already active');
      return true;
    }

    // If locked by different context, handle conflict
    if (_isLocked && _activeContext != null && _activeContext != context) {
      return await _handleConflict(context, engine);
    }

    // Grant access
    await _grantAccess(context, engine);
    return true;
  }

  /// Release access for a TTS operation
  Future<void> releaseAccess(TtsContext context, TtsEngineType engine) async {
    if (_activeContext == context && _activeEngine == engine) {
      AnxLog.info('TTS access released for context: $context, engine: $engine');

      _activeContext = null;
      _activeEngine = null;
      _operationStartTime = null;
      _isLocked = false;

      if (_currentOperation != null && !_currentOperation!.isCompleted) {
        _currentOperation!.complete();
        _currentOperation = null;
      }

      _contextNotifier.value = null;
    }
  }

  /// Force release access (emergency stop with safety checks)
  Future<void> forceRelease() async {
    AnxLog.warning('Force releasing TTS access');

    _activeContext = null;
    _activeEngine = null;
    _operationStartTime = null;
    _isLocked = false;

    if (_currentOperation != null && !_currentOperation!.isCompleted) {
      _currentOperation!.complete();
      _currentOperation = null;
    }

    _contextNotifier.value = null;
  }

  /// Check if operation has timed out (for cleanup)
  bool get hasTimedOut {
    if (_operationStartTime == null) return false;

    const timeoutDuration = Duration(minutes: 5); // Reasonable timeout
    return DateTime.now().difference(_operationStartTime!) > timeoutDuration;
  }

  /// Cleanup timed out operations
  Future<void> cleanupTimedOut() async {
    if (hasTimedOut) {
      AnxLog.warning(
        'Cleaning up timed out TTS operation for context: $_activeContext',
      );
      await forceRelease();
    }
  }

  /// Validate TTS context and engine combination with differentiation logic
  bool _isValidCombination(TtsContext context, TtsEngineType engine) {
    // Dictionary pronunciation should only use System TTS (FlutterTts)
    if (context == TtsContext.dictionaryPronunciation &&
        engine != TtsEngineType.systemTts) {
      AnxLog.warning(
        'Invalid combination: Dictionary pronunciation must use System TTS',
      );
      return false;
    }

    // Continuous reading should use EdgeTts when System TTS is enabled (for separation)
    if (context == TtsContext.continuousReading &&
        Prefs().isSystemTts &&
        engine == TtsEngineType.systemTts) {
      AnxLog.warning(
        'Engine differentiation active: Continuous reading should use EdgeTts when System TTS is enabled',
      );
      return false;
    }

    return true;
  }

  Future<void> switchTtsType(bool useSystemTts) async {
    if (Prefs().isSystemTts == useSystemTts) return;

    // Safety check: ensure no active operations before switching
    if (_isLocked && _activeContext != null) {
      AnxLog.warning(
        'Cannot switch TTS type while operation is active: $_activeContext',
      );

      // Force cleanup if operation has timed out
      if (hasTimedOut) {
        await cleanupTimedOut();
      } else {
        // Wait briefly for current operation to complete
        await Future<void>.delayed(const Duration(milliseconds: 500));
        if (_isLocked) {
          AnxLog.warning('Forcing TTS switch due to persistent lock');
          await forceRelease();
        }
      }
    }

    if (_currentTts != null) {
      await _currentTts!.stop();
      await _currentTts!.dispose();
      _currentTts = null;
    }

    Prefs().isSystemTts = useSystemTts;
    _currentTts = createTtsWithDifferentiation();
  }

  /// Handle conflict between TTS contexts
  Future<bool> _handleConflict(
    TtsContext requestingContext,
    TtsEngineType requestingEngine,
  ) async {
    final currentContext = _activeContext!;
    final currentEngine = _activeEngine!;

    AnxLog.warning(
      'TTS conflict detected: $currentContext ($currentEngine) vs $requestingContext ($requestingEngine)',
    );

    // Dictionary pronunciation has priority over continuous reading
    if (requestingContext == TtsContext.dictionaryPronunciation &&
        currentContext == TtsContext.continuousReading) {
      AnxLog.info(
        'Dictionary pronunciation taking priority over continuous reading',
      );
      await _grantAccess(requestingContext, requestingEngine);
      return true;
    }

    // If continuous reading is requesting while dictionary is active, deny
    if (requestingContext == TtsContext.continuousReading &&
        currentContext == TtsContext.dictionaryPronunciation) {
      AnxLog.info(
        'Denying continuous reading access - dictionary pronunciation active',
      );

      final error = TtsError(
        context: requestingContext,
        failedEngine: requestingEngine,
        type: TtsErrorType.resourceEngineInUse,
        message:
            'TTS engine is currently being used for dictionary pronunciation',
        severity: TtsErrorSeverity.low,
        technicalDetails:
            'Active context: $currentContext, Active engine: $currentEngine',
      );

      error.log();
      return false;
    }

    // Same context but different engine - allow switch
    if (requestingContext == currentContext &&
        requestingEngine != currentEngine) {
      AnxLog.info('Allowing engine switch within same context');
      await _grantAccess(requestingContext, requestingEngine);
      return true;
    }

    // Default: deny access
    AnxLog.warning('Access denied for $requestingContext ($requestingEngine)');
    return false;
  }

  /// Grant access to a TTS operation
  Future<void> _grantAccess(TtsContext context, TtsEngineType engine) async {
    _activeContext = context;
    _activeEngine = engine;
    _operationStartTime = DateTime.now();
    _isLocked = true;
    _currentOperation = Completer<void>();

    _contextNotifier.value = context;

    AnxLog.info('TTS access granted to context: $context, engine: $engine');
  }

  Future<void> dispose() async {
    if (_currentTts != null) {
      await _currentTts!.stop();
      await _currentTts!.dispose();
      _currentTts = null;
    }

    _contextNotifier.dispose();
    if (_currentOperation != null && !_currentOperation!.isCompleted) {
      _currentOperation!.complete();
    }
  }

  ValueNotifier<TtsStateEnum> get ttsStateNotifier {
    return current.ttsStateNotifier;
  }
}
